insert into
  attendance (
    entity_identifier,
    entity_type,
    state,
    priority,
    created_at,
    updated_at
  )
select
  student_dni,
  's',
  'falta',
  :turn,
  now(),
  now()
from
  register
where
  section_code like any(array[:list])
  and priority in (:turn, 0)
  and state = 'a'
  and not exists (
    select
      *
    from
      attendance
    where
      student_dni = entity_identifier
      and created_at::date = current_date
      and priority = :turn
  )