insert into
  attendance (
    entity_identifier,
    entity_type,
    state,
    priority,
    created_at,
    updated_at,
    branch_code
  )
select
  teacher.dni as entity_identifier,
  't' as entity_type,
  'falta' as state,
  1 as priority,
  now(),
  now(),
  teacher.branch_code as branch_code
from
  teacher
where
  not exists(
    select
      *
    from
      attendance
    where
      teacher.dni = entity_identifier
      and created_at::date = current_date
  )
  and teacher.state = true
  and branch_code = 1