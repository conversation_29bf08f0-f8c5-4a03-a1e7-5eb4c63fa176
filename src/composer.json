{"name": "aeduca/admin", "version": "7", "type": "project", "description": "Aeduca admin", "license": "MIT", "require": {"php": "^8.3", "barryvdh/laravel-dompdf": "^2.2.0", "guzzlehttp/guzzle": "^7.9.2", "laravel/framework": "^11.21.0", "laravel/helpers": "^1.7.0", "laravel/tinker": "^2.9.0", "maatwebsite/excel": "^3.1.55", "php-open-source-saver/jwt-auth": "^2.7.1", "simplesoftwareio/simple-qrcode": "^4.2.0", "predis/predis": "^1.1.10"}, "require-dev": {"spatie/laravel-ignition": "^2.8.0", "nunomaduro/collision": "^8.4.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "stable", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}