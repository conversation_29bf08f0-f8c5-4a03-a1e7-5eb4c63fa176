<?php

namespace App\Console\Commands;

use App\Cache\ConfigCache;
use Illuminate\Console\Command;

class AbsencesTeacherCommand extends Command
{
    protected $signature = "absence:teacher";

    protected $description = "Register teacher absences";


    public function handle(): int
    {
        if (!ConfigCache::getCoreConfig()["abs_enabled"]) {
            return 0;
        }

        $content = file_get_contents(database_path() . "/abst.sql");

        if (empty($content)) {
            return 0;
        }

        $formated = str_replace(["\n", "   ", "  "], " ", $content);

        return \DB::affectingStatement($formated);
    }
}
