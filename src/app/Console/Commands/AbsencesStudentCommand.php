<?php

namespace App\Console\Commands;

use App\Cache\ConfigCache;
use Illuminate\Console\Command;

class AbsencesStudentCommand extends Command
{
    protected $signature = "absence:student {--turn=1}";

    protected $description = "Register Student Absences by turn";


    public function handle(): int
    {

        if (!ConfigCache::getCoreConfig()["abs_enabled"]) {
            return 0;
        }

        $turn = intval($this->option("turn"));
        $cached = ConfigCache::getCoreConfig()["abs" . $turn];
        $list = sprintf("'%s'", collect($cached)->join("','"));

        if (empty($list)) {
            return 0;
        }

        $content = file_get_contents(database_path() . "/abs.sql");

        if (empty($content)) {
            return 0;
        }

        $formated = str_replace(["\n", "   ", "  "], " ", $content);
        $bindingList = str_replace(":list", $list, $formated);
        $bindingTurn = str_replace(":turn", $turn, $bindingList);

        return \DB::affectingStatement($bindingTurn);
    }
}
