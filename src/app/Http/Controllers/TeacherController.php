<?php

namespace App\Http\Controllers;

use App\Http\Requests\PersonRequest;
use App\Repositories\TeacherRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class TeacherController extends Controller
{
    private TeacherRepository $instance;

    public function __construct(TeacherRepository $instance)
    {
        $this->instance = $instance;
    }

    public function fetchBySpe(string $spe, string $state)
    {
        return response()->json([
            "values" => $this->instance->fetchBySpe($spe, $state === "true"),
        ]);
    }

    public function self(string $dni)
    {
        return response()->json([
            "value" => $this->instance->self($dni),
        ]);
    }

    public function show(string $dni)
    {
        try {
            return response()->json([
                "value" => $this->instance->fetch($dni),
            ]);
        } catch (ModelNotFoundException $e) {
            return response()->json([
                "message" => "No hay resultados para el dni",
            ], 404);
        }
    }

    public function search(string $name)
    {
        return response()->json([
            "values" => $this->instance->search($name),
        ]);
    }

    public function store(PersonRequest $request)
    {
        $this->instance->store($request->all());
        return response()->json([
            "message" => "Correctamente guardado",
        ]);
    }

    public function update(PersonRequest $request, string $dni)
    {
        $this->instance->update($request->all(), $dni);
        return response()->json([
            "message" => "Correctamente actualizado",
        ]);
    }

    public function changeState(string $dni)
    {
        $this->instance->changeState($dni);
        return response()->json([
            "message" => "Correctamente actualizado",
        ]);
    }
}
