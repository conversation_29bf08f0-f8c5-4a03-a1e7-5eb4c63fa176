<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CycleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "type" => "required|min:2|max:2",
            "title" => "required",
            "from" => "required|date",
            "to" => "required|date",
            "modular_code" => "max:20",
            "attendance" => "required",
            "monthly" => "required",
        ];
    }
}
