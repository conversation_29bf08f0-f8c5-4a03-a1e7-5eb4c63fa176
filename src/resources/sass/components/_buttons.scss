.btn {
    transition: transform 0.2s;

    &:not(:last-of-type) {
        margin-right: 0.5rem;
    }

    &:not(.btn-icon) {
        i {
            margin-right: 0.3rem;
        }
    }

    &:active {
        transform: scale(1.1);
    }

    &.btn-icon {
        width: 40px;
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
            font-size: 1.3rem;
        }
    }

}


@each $color,
$value in $theme-colors {
    .btn-inverse-#{$color} {
        background-color: rgba($value, 0.25);
        background-image: none;
        border-bottom: 4px solid rgba($value, 1);
        color: darken($color: $value, $amount: 15%);
    }

    .btn-#{$color} {
        color: $white;
    }

    .btn-inverse-#{$color},
    .btn-outline-#{$color} {
        &:hover,
        &:active,
        &:focus {
            color: $white !important;
            background-color: $value !important;
            border-color: $value !important;
        }
    }

    .btn-inverse-#{$color},
    .btn-#{$color} {
        &:hover {
            box-shadow: rgba($value, 0.7) 1px 1px 20px;
        }

        &:disabled {
            color: $value;
            background-color: rgba($value, 5%);
            border: 1px solid $value;
        }
    }

    @include color-mode(dark) {
        .btn-inverse-#{$color} {
            color: lighten($value, 25%);

            &:disabled {
                color: $white !important;
            }
        }
    }
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active {
    color: $white !important;
}

.btn-warning,
.btn-info {
    color: darken($info, 60%);
}

.fixed-action-btn {
    position: fixed;
    z-index: 998;
    right: 35px;
    bottom: 35px;
    margin-bottom: 0;
}